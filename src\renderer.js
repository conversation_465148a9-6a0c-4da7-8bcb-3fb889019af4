// renderer.js

// --- 1. Get references to UI elements ---
const refineBtn = document.getElementById('refine-btn');
const copyBtn = document.getElementById('copy-btn');
const clearBtn = document.getElementById('clear-btn');
const settingsBtn = document.getElementById('settings-btn');
const inputText = document.getElementById('input-text');
const outputText = document.getElementById('output-text');
const langSelect = document.getElementById('lang-select');
const toneSelect = document.getElementById('tone-select');
const actionSelect = document.getElementById('action-select');
const charCount = document.getElementById('char-count');
const loadingOverlay = document.querySelector('.loading-overlay');
const statusDot = document.querySelector('.status-dot');
const statusText = document.querySelector('.status-text');
const copyIcon = document.querySelector('.copy-icon');
const copyText = document.querySelector('.copy-text');

// Settings Modal Elements
const settingsModal = document.getElementById('settings-modal');
const closeSettingsBtn = document.getElementById('close-settings-btn');
const providerSelect = document.getElementById('provider-select');
const modelSelect = document.getElementById('model-select');
const refreshModelsBtn = document.getElementById('refresh-models-btn');
const openrouterSettings = document.getElementById('openrouter-settings');
const apiKeyInput = document.getElementById('api-key-input');
const toggleKeyVisibility = document.getElementById('toggle-key-visibility');
const autoSaveSettings = document.getElementById('auto-save-settings');
const showCharCount = document.getElementById('show-char-count');
const resetSettingsBtn = document.getElementById('reset-settings-btn');
const saveSettingsBtn = document.getElementById('save-settings-btn');

// --- 2. Configuration and Model Data ---
const API_PROVIDERS = {
    ollama: {
        name: 'Ollama (Local)',
        baseUrl: 'http://localhost:11434',
        modelsEndpoint: '/api/tags',
        models: [] // Will be populated dynamically
    },
    openrouter: {
        name: 'OpenRouter (Cloud)',
        baseUrl: 'https://openrouter.ai/api/v1',
        modelsEndpoint: '/models',
        models: [] // Will be populated dynamically
    }
};

// Cache for models to avoid repeated API calls
const modelCache = {
    ollama: { models: [], lastFetch: 0, ttl: 300000 }, // 5 minutes TTL
    openrouter: { models: [], lastFetch: 0, ttl: 3600000 } // 1 hour TTL
};

// Default fallback models in case API calls fail
const FALLBACK_MODELS = {
    ollama: [
        { id: 'gemma:2b-instruct', name: 'Gemma 2B Instruct' },
        { id: 'llama3.2:latest', name: 'Llama 3.2 Latest' },
        { id: 'mistral:latest', name: 'Mistral Latest' }
    ],
    openrouter: [
        { id: 'google/gemma-2-9b-it', name: 'Gemma 2 9B IT' },
        { id: 'meta-llama/llama-3.1-8b-instruct', name: 'Llama 3.1 8B Instruct' },
        { id: 'anthropic/claude-3-haiku', name: 'Claude 3 Haiku' },
        { id: 'openai/gpt-4o-mini', name: 'GPT-4o Mini' }
    ]
};

// --- 3. Helper Functions ---
function updateCharCount() {
    const count = inputText.value.length;
    charCount.textContent = count.toLocaleString();

    // Update character count color based on length
    if (count > 5000) {
        charCount.style.color = 'var(--error-color)';
    } else if (count > 3000) {
        charCount.style.color = 'var(--warning-color)';
    } else {
        charCount.style.color = 'var(--gray-500)';
    }
}

// --- 4. Model Fetching Functions ---
async function fetchOllamaModels() {
    try {
        const response = await fetch(`${API_PROVIDERS.ollama.baseUrl}${API_PROVIDERS.ollama.modelsEndpoint}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data.models.map(model => ({
            id: model.name,
            name: formatModelName(model.name),
            size: model.size,
            modified: model.modified_at
        })).sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
        console.warn('Failed to fetch Ollama models:', error);
        return FALLBACK_MODELS.ollama;
    }
}

async function fetchOpenRouterModels() {
    try {
        const response = await fetch(`${API_PROVIDERS.openrouter.baseUrl}${API_PROVIDERS.openrouter.modelsEndpoint}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        // Filter for text models and popular ones
        return data.data
            .filter(model =>
                model.architecture?.output_modalities?.includes('text') &&
                !model.id.includes('vision') &&
                !model.id.includes('embedding') &&
                parseFloat(model.pricing?.prompt || '0') > 0 // Has pricing info
            )
            .map(model => ({
                id: model.id,
                name: model.name || formatModelName(model.id),
                context: model.context_length,
                pricing: model.pricing
            }))
            .sort((a, b) => a.name.localeCompare(b.name))
            .slice(0, 200); // Limit to top 50 models
    } catch (error) {
        console.warn('Failed to fetch OpenRouter models:', error);
        return FALLBACK_MODELS.openrouter;
    }
}

function formatModelName(modelId) {
    // Convert model IDs to readable names
    return modelId
        .replace(/[_-]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
        .replace(/(\d+)([a-zA-Z])/g, '$1 $2')
        .replace(/\s+/g, ' ')
        .trim();
}

async function getModelsForProvider(provider) {
    const cache = modelCache[provider];
    const now = Date.now();

    // Check if we have cached models that are still valid
    if (cache.models.length > 0 && (now - cache.lastFetch) < cache.ttl) {
        return cache.models;
    }

    // Fetch fresh models
    let models;
    if (provider === 'ollama') {
        models = await fetchOllamaModels();
    } else if (provider === 'openrouter') {
        models = await fetchOpenRouterModels();
    } else {
        models = FALLBACK_MODELS[provider] || [];
    }

    // Update cache
    cache.models = models;
    cache.lastFetch = now;

    return models;
}

async function updateModelOptions() {
    const selectedProvider = providerSelect.value;

    // Show loading state
    modelSelect.innerHTML = '<option value="">Loading models...</option>';
    modelSelect.disabled = true;

    try {
        const models = await getModelsForProvider(selectedProvider);

        // Clear existing options
        modelSelect.innerHTML = '';

        if (models.length === 0) {
            modelSelect.innerHTML = '<option value="">No models available</option>';
        } else {
            // Add new options
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                if (model.context) {
                    option.title = `Context: ${model.context.toLocaleString()} tokens`;
                }
                modelSelect.appendChild(option);
            });
        }

        modelSelect.disabled = false;

    } catch (error) {
        console.error('Error updating model options:', error);
        modelSelect.innerHTML = '<option value="">Error loading models</option>';
        modelSelect.disabled = false;
    }

    // Show/hide OpenRouter settings section
    if (selectedProvider === 'openrouter') {
        openrouterSettings.style.display = 'block';
    } else {
        openrouterSettings.style.display = 'none';
    }
}

function toggleApiKeyVisibility() {
    const isPassword = apiKeyInput.type === 'password';
    apiKeyInput.type = isPassword ? 'text' : 'password';
    toggleKeyVisibility.textContent = isPassword ? '🙈' : '👁️';
}

function saveApiKey() {
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
        localStorage.setItem('openrouter_api_key', apiKey);
    }
}

function loadApiKey() {
    const savedKey = localStorage.getItem('openrouter_api_key');
    if (savedKey) {
        apiKeyInput.value = savedKey;
    }
}

// --- Settings Modal Functions ---
function openSettingsModal() {
    settingsModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeSettingsModal() {
    settingsModal.classList.remove('active');
    document.body.style.overflow = '';
}

function loadSettings() {
    // Load provider and model settings
    const savedProvider = localStorage.getItem('ai_provider') || 'ollama';
    const savedModel = localStorage.getItem('ai_model');
    const savedLanguage = localStorage.getItem('language') || 'English';
    const savedTone = localStorage.getItem('tone') || 'professional';
    const savedAction = localStorage.getItem('action') || 'improve-style';

    // Load app settings
    const autoSave = localStorage.getItem('auto_save_settings') !== 'false';
    const showChar = localStorage.getItem('show_char_count') !== 'false';

    // Apply settings
    providerSelect.value = savedProvider;
    langSelect.value = savedLanguage;
    toneSelect.value = savedTone;
    actionSelect.value = savedAction;
    autoSaveSettings.checked = autoSave;
    showCharCount.checked = showChar;

    if (savedModel) {
        modelSelect.value = savedModel;
    }

    // Apply character count visibility
    const charCountElement = document.querySelector('.char-counter');
    if (charCountElement) {
        charCountElement.style.display = showChar ? 'block' : 'none';
    }
}

function saveSettings() {
    if (autoSaveSettings.checked) {
        localStorage.setItem('ai_provider', providerSelect.value);
        localStorage.setItem('ai_model', modelSelect.value);
        localStorage.setItem('language', langSelect.value);
        localStorage.setItem('tone', toneSelect.value);
        localStorage.setItem('action', actionSelect.value);
        localStorage.setItem('auto_save_settings', autoSaveSettings.checked);
        localStorage.setItem('show_char_count', showCharCount.checked);
    }

    // Apply character count visibility
    const charCountElement = document.querySelector('.char-counter');
    if (charCountElement) {
        charCountElement.style.display = showCharCount.checked ? 'block' : 'none';
    }
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        // Clear localStorage
        localStorage.removeItem('ai_provider');
        localStorage.removeItem('ai_model');
        localStorage.removeItem('language');
        localStorage.removeItem('tone');
        localStorage.removeItem('action');
        localStorage.removeItem('auto_save_settings');
        localStorage.removeItem('show_char_count');
        localStorage.removeItem('openrouter_api_key');

        // Reset to defaults
        providerSelect.value = 'ollama';
        langSelect.value = 'English';
        toneSelect.value = 'professional';
        actionSelect.value = 'improve-style';
        autoSaveSettings.checked = true;
        showCharCount.checked = true;
        apiKeyInput.value = '';

        // Refresh models
        updateModelOptions();

        // Apply settings
        saveSettings();

        alert('Settings have been reset to defaults.');
    }
}

function setLoadingState(isLoading) {
    if (isLoading) {
        refineBtn.classList.add('loading');
        refineBtn.disabled = true;
        copyBtn.disabled = true;
        loadingOverlay.classList.add('active');
        statusDot.style.backgroundColor = 'var(--warning-color)';
        statusText.textContent = 'Processing...';
    } else {
        refineBtn.classList.remove('loading');
        refineBtn.disabled = false;
        loadingOverlay.classList.remove('active');
        statusDot.style.backgroundColor = 'var(--success-color)';
        statusText.textContent = 'Ready';

        // Only enable copy if there's actual output
        if (outputText.value && !outputText.value.includes('Error:') && !outputText.value.includes('Please enter')) {
            copyBtn.disabled = false;
        }
    }
}

function setErrorState(message) {
    statusDot.style.backgroundColor = 'var(--error-color)';
    statusText.textContent = 'Error';
    outputText.value = message;
    setLoadingState(false);
}

// --- 4. API Functions ---
async function callOllamaAPI(model, prompt) {
    // First check if Ollama is running
    try {
        await fetch(`${API_PROVIDERS.ollama.baseUrl}/api/tags`, { method: 'GET' });
    } catch (error) {
        throw new Error('Ollama server is not running. Please start Ollama and try again.');
    }

    const response = await fetch(`${API_PROVIDERS.ollama.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: model,
            prompt: prompt,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9,
                top_k: 40,
                num_predict: 4000
            }
        })
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        let errorMessage = `Ollama API error: ${response.status} ${response.statusText}`;

        if (errorData.error) {
            errorMessage += ` - ${errorData.error}`;
        }

        // Add specific error handling
        if (response.status === 404) {
            errorMessage += `\nModel "${model}" not found. Please pull the model first: ollama pull ${model}`;
        } else if (response.status === 500) {
            errorMessage += '\nOllama server error. Please check the server logs.';
        }

        throw new Error(errorMessage);
    }

    const data = await response.json();

    if (!data.response) {
        throw new Error('Invalid response format from Ollama API');
    }

    return data.response.trim();
}

async function callOpenRouterAPI(model, prompt, apiKey) {
    const response = await fetch(`${API_PROVIDERS.openrouter.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': window.location.origin || 'https://smart-scribe.app',
            'X-Title': 'Smart Scribe - AI Writing Assistant'
        },
        body: JSON.stringify({
            model: model,
            messages: [
                {
                    role: 'system',
                    content: 'You are Smart Scribe, an expert writing assistant. Follow the user\'s instructions precisely and return only the refined text without explanations.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 4000,
            stream: false,
            // Add optional parameters for better results
            top_p: 0.9,
            frequency_penalty: 0.1,
            presence_penalty: 0.1
        })
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        let errorMessage = `OpenRouter API error: ${response.status} ${response.statusText}`;

        if (errorData.error) {
            if (errorData.error.message) {
                errorMessage += ` - ${errorData.error.message}`;
            }
            if (errorData.error.code) {
                errorMessage += ` (Code: ${errorData.error.code})`;
            }
        }

        // Add specific error handling for common issues
        if (response.status === 401) {
            errorMessage += '\nPlease check your API key.';
        } else if (response.status === 402) {
            errorMessage += '\nInsufficient credits. Please add credits to your OpenRouter account.';
        } else if (response.status === 429) {
            errorMessage += '\nRate limit exceeded. Please try again in a moment.';
        }

        throw new Error(errorMessage);
    }

    const data = await response.json();

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from OpenRouter API');
    }

    return data.choices[0].message.content.trim();
}

async function refineText(userText, language, tone, action) {
    const provider = providerSelect.value;
    const model = modelSelect.value;
    const prompt = buildPrompt(userText, language, tone, action);

    if (provider === 'ollama') {
        return await callOllamaAPI(model, prompt);
    } else if (provider === 'openrouter') {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            throw new Error('OpenRouter API key is required. Please enter your API key.');
        }
        saveApiKey(); // Save the API key for future use
        return await callOpenRouterAPI(model, prompt, apiKey);
    } else {
        throw new Error('Invalid API provider selected.');
    }
}

// --- 5. Event Listeners ---

// Settings Modal
settingsBtn.addEventListener('click', openSettingsModal);
closeSettingsBtn.addEventListener('click', closeSettingsModal);

// Close modal when clicking outside
settingsModal.addEventListener('click', (e) => {
    if (e.target === settingsModal) {
        closeSettingsModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && settingsModal.classList.contains('active')) {
        closeSettingsModal();
    }
});

// Provider selection change
providerSelect.addEventListener('change', () => {
    updateModelOptions();
    if (autoSaveSettings.checked) {
        saveSettings();
    }
});

// Refresh models button
refreshModelsBtn.addEventListener('click', async () => {
    const provider = providerSelect.value;

    // Clear cache for the current provider
    modelCache[provider].models = [];
    modelCache[provider].lastFetch = 0;

    // Show loading state
    refreshModelsBtn.disabled = true;
    refreshModelsBtn.classList.add('loading');

    try {
        await updateModelOptions();
    } finally {
        refreshModelsBtn.disabled = false;
        refreshModelsBtn.classList.remove('loading');
    }
});

// API key visibility toggle
toggleKeyVisibility.addEventListener('click', toggleApiKeyVisibility);

// API key input (save on change)
apiKeyInput.addEventListener('change', saveApiKey);

// Settings form elements
langSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

toneSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

actionSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

modelSelect.addEventListener('change', () => {
    if (autoSaveSettings.checked) saveSettings();
});

// Settings buttons
resetSettingsBtn.addEventListener('click', resetSettings);
saveSettingsBtn.addEventListener('click', () => {
    saveSettings();
    closeSettingsModal();

    // Show feedback
    const originalText = saveSettingsBtn.querySelector('.button-icon').textContent;
    saveSettingsBtn.querySelector('.button-icon').textContent = '✅';
    setTimeout(() => {
        saveSettingsBtn.querySelector('.button-icon').textContent = originalText;
    }, 1000);
});

// Auto-save toggle
autoSaveSettings.addEventListener('change', () => {
    localStorage.setItem('auto_save_settings', autoSaveSettings.checked);
});

// Character count toggle
showCharCount.addEventListener('change', () => {
    const charCountElement = document.querySelector('.char-counter');
    if (charCountElement) {
        charCountElement.style.display = showCharCount.checked ? 'block' : 'none';
    }
    if (autoSaveSettings.checked) saveSettings();
});

// Character count update
inputText.addEventListener('input', updateCharCount);

// Clear button
clearBtn.addEventListener('click', () => {
    inputText.value = '';
    outputText.value = '';
    updateCharCount();
    copyBtn.disabled = true;

    // Reset copy button state
    copyIcon.textContent = '📋';
    copyText.textContent = 'Copy';
    copyBtn.classList.remove('copied');
});

// --- 6. Event Listener for "Refine Text" button ---
refineBtn.addEventListener('click', async () => {
    const userText = inputText.value.trim();

    // Get selected values from dropdowns
    const language = langSelect.value;
    const tone = toneSelect.value;
    const action = actionSelect.value;
    const provider = providerSelect.value;

    // Basic validation: Prevent running on empty text
    if (!userText) {
        setErrorState("Please enter some text to refine.");
        return;
    }

    // Check text length
    if (userText.length > 10000) {
        setErrorState("Text is too long. Please limit to 10,000 characters.");
        return;
    }

    // Validate model selection
    if (!modelSelect.value) {
        setErrorState("Please select a model first.");
        return;
    }

    // Validate API key for OpenRouter
    if (provider === 'openrouter' && !apiKeyInput.value.trim()) {
        setErrorState("Please enter your OpenRouter API key to use cloud models.");
        apiKeyInput.focus();
        return;
    }

    // Show loading state
    setLoadingState(true);

    try {
        const refinedText = await refineText(userText, language, tone, action);
        outputText.value = refinedText;
        setLoadingState(false);

    } catch (error) {
        console.error("Error communicating with API:", error);

        let errorMessage = `Error: ${error.message}`;

        if (provider === 'ollama') {
            errorMessage += `\n\nMake sure Ollama is running and the selected model is downloaded.\nTo install: ollama pull ${modelSelect.value}`;
        } else if (provider === 'openrouter') {
            errorMessage += `\n\nPlease check your API key and internet connection.\nGet your API key from: https://openrouter.ai/keys`;
        }

        setErrorState(errorMessage);
    }
});

// --- 6. Event Listener for "Copy to Clipboard" button ---
copyBtn.addEventListener('click', async () => {
    const textToCopy = outputText.value;
    if (textToCopy && !textToCopy.includes('Error:') && !textToCopy.includes('Please enter')) {
        try {
            await navigator.clipboard.writeText(textToCopy);

            // Provide visual feedback
            copyIcon.textContent = '✅';
            copyText.textContent = 'Copied!';
            copyBtn.classList.add('copied');

            // Reset after 2 seconds
            setTimeout(() => {
                copyIcon.textContent = '📋';
                copyText.textContent = 'Copy';
                copyBtn.classList.remove('copied');
            }, 2000);

        } catch (err) {
            console.error('Failed to copy text: ', err);

            // Fallback for older browsers or permission issues
            try {
                // Create a temporary textarea element
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = textToCopy;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Show success feedback
                copyIcon.textContent = '✅';
                copyText.textContent = 'Copied!';
                copyBtn.classList.add('copied');

                setTimeout(() => {
                    copyIcon.textContent = '📋';
                    copyText.textContent = 'Copy';
                    copyBtn.classList.remove('copied');
                }, 2000);

            } catch (fallbackErr) {
                console.error('Fallback copy failed: ', fallbackErr);
                alert('Could not copy text. Please copy manually.');
            }
        }
    }
});


// --- 7. Keyboard Shortcuts ---
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to refine text
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!refineBtn.disabled && inputText.value.trim()) {
            refineBtn.click();
        }
    }

    // Ctrl/Cmd + K to clear text
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        clearBtn.click();
    }

    // Alt + S to open settings
    if (e.altKey && e.key === 's') {
        e.preventDefault();
        if (!settingsModal.classList.contains('active')) {
            openSettingsModal();
        }
    }

    // Ctrl/Cmd + C when output is focused to copy
    if ((e.ctrlKey || e.metaKey) && e.key === 'c' && document.activeElement === outputText) {
        e.preventDefault();
        if (!copyBtn.disabled) {
            copyBtn.click();
        }
    }
});

// --- 8. Initialize UI ---
async function initializeUI() {
    updateCharCount();
    copyBtn.disabled = true;

    // Load saved settings
    loadSettings();
    loadApiKey();

    // Add tooltips for keyboard shortcuts
    refineBtn.title = 'Refine Text (Ctrl+Enter)';
    clearBtn.title = 'Clear Text (Ctrl+K)';
    refreshModelsBtn.title = 'Refresh available models';
    settingsBtn.title = 'Open Settings (Alt+S)';

    // Initialize model options based on selected provider
    try {
        await updateModelOptions();
    } catch (error) {
        console.error('Failed to initialize models:', error);
        // Show fallback models
        const provider = providerSelect.value;
        const fallbackModels = FALLBACK_MODELS[provider] || [];

        modelSelect.innerHTML = '';
        if (fallbackModels.length > 0) {
            fallbackModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
        } else {
            modelSelect.innerHTML = '<option value="">No models available</option>';
        }
    }

    // Focus on input text area
    inputText.focus();
}

// --- 9. Function to build the detailed prompt ---
function buildPrompt(userText, language, tone, action) {
    // Detailed descriptions for each Tone option
    const toneDescriptions = {
        professional: "Formal and professional for a work environment.",
        casual: "Casual and friendly, as if speaking to a friend.",
        confident: "Strong, assertive, and self-assured.",
        friendly: "Warm, approachable, and personable."
    };

    // Detailed descriptions for each Action option
    const actionDescriptions = {
        'improve-style': "Improve the overall style, clarity, and professionalism of the text. Ensure natural flow and appropriate vocabulary.",
        'fix-grammar': "Correct all grammar, spelling, and punctuation errors without changing the original meaning or style significantly.",
        'shorten': "Make the text more concise and to the point, removing redundant words or phrases while retaining key information.",
        'expand': "Elaborate on the original text, adding more detail, examples, or context to make it more comprehensive."
    };

    // Construct the full prompt string
    return `You are Smart Scribe, an expert multilingual writing assistant. Your task is to refine the user's text based on the provided instructions. Follow these rules strictly:

1. Language: Respond in ${language}
2. Tone: Use a ${tone} tone - ${toneDescriptions[tone]}
3. Action: ${actionDescriptions[action]}
4. Only return the refined text, no explanations or additional commentary.
5. Maintain the original meaning and intent while applying the requested changes.

Original text to refine:
${userText}

Refined text:`;
}

// --- 10. Initialize the application ---
document.addEventListener('DOMContentLoaded', initializeUI);