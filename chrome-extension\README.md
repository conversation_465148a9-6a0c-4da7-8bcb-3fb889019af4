# Smart Scribe - Chrome Extension

A powerful AI writing assistant browser extension that helps you improve, fix, expand, or shorten your text using multiple AI providers.

## Features

### 🚀 **Multiple AI Providers**
- **<PERSON>llama (Local)**: Use local AI models for privacy and offline usage
- **OpenRouter (Cloud)**: Access to various cloud-based AI models

### ✨ **Text Enhancement Actions**
- **Improve Style**: Enhance writing clarity and flow
- **Fix Grammar**: Correct grammar, spelling, and punctuation
- **Shorten**: Make text more concise
- **Expand**: Add detail and elaboration

### 🎯 **Multiple Usage Methods**
1. **Popup Interface**: Click the extension icon for full interface
2. **Right-click Context Menu**: Select text and choose action from context menu
3. **Text Field Integration**: Floating widget appears when focusing on text areas
4. **Quick Selection**: Select text to show quick action button

### ⚙️ **Smart Features**
- **Default Model Selection**: Set your preferred AI model as default
- **Dynamic UI**: Interface adapts to show current AI model name
- **Settings Persistence**: All preferences saved across browser sessions
- **Responsive Design**: Works well on all screen sizes

## Installation

### Method 1: Load Unpacked Extension (Development)

1. **Open Chrome Extensions Page**:
   - Go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)

2. **Load the Extension**:
   - Click "Load unpacked"
   - Select the `chrome-extension` folder
   - The extension should appear in your extensions list

3. **Pin the Extension** (Optional):
   - Click the puzzle piece icon in Chrome toolbar
   - Pin Smart Scribe for easy access

### Method 2: Create Extension Package

1. **Zip the Extension**:
   ```bash
   cd chrome-extension
   zip -r smart-scribe-extension.zip . -x "*.DS_Store" "README.md"
   ```

2. **Load in Developer Mode**:
   - Follow Method 1 but select the zip file instead

## Setup

### For Ollama (Local AI)

1. **Install Ollama**:
   - Download from [ollama.ai](https://ollama.ai)
   - Install and start the service

2. **Pull Models**:
   ```bash
   ollama pull llama3.2:3b
   ollama pull gemma2:2b
   ```

3. **Configure Extension**:
   - Click the Smart Scribe icon
   - Click settings (⚙️)
   - Select "Ollama (Local)" as provider
   - Choose your preferred model

### For OpenRouter (Cloud AI)

1. **Get API Key**:
   - Visit [OpenRouter Dashboard](https://openrouter.ai/keys)
   - Create an account and generate API key

2. **Configure Extension**:
   - Click the Smart Scribe icon
   - Click settings (⚙️)
   - Select "OpenRouter (Cloud)" as provider
   - Enter your API key
   - Choose your preferred model

## Usage

### Popup Interface
1. Click the Smart Scribe extension icon
2. Enter or paste your text
3. Select action, tone, and language
4. Click "Refine Text"
5. Copy the improved text

### Context Menu
1. Select any text on a webpage
2. Right-click and choose "Smart Scribe"
3. Select the desired action
4. Text will be replaced automatically

### Text Field Integration
1. Click on any text area or input field
2. Floating action buttons will appear
3. Click the desired action button
4. Text will be processed and replaced

### Quick Selection
1. Select text on any webpage
2. "Smart Scribe" button appears
3. Click for action menu
4. Choose desired action

## File Structure

```
chrome-extension/
├── manifest.json          # Extension configuration
├── src/
│   ├── popup.html         # Main popup interface
│   ├── popup.css          # Popup styling
│   ├── popup.js           # Popup functionality
│   ├── background.js      # Background service worker
│   ├── content.js         # Content script for web pages
│   └── content.css        # Content script styling
├── icons/                 # Extension icons (16, 32, 48, 128px)
└── README.md             # This file
```

## Permissions Explained

- **storage**: Save user preferences and settings
- **activeTab**: Access current tab for text processing
- **contextMenus**: Add right-click menu options
- **scripting**: Inject content scripts for text replacement
- **host_permissions**: Access to Ollama (localhost) and OpenRouter APIs

## Privacy & Security

- **Local Processing**: When using Ollama, all text processing happens locally
- **Secure Storage**: API keys stored securely in Chrome's sync storage
- **No Data Collection**: Extension doesn't collect or transmit user data
- **Open Source**: All code is transparent and auditable

## Troubleshooting

### Ollama Issues
- Ensure Ollama service is running: `ollama serve`
- Check if models are installed: `ollama list`
- Verify localhost:11434 is accessible

### OpenRouter Issues
- Verify API key is correct and has credits
- Check internet connection
- Ensure selected model is available

### General Issues
- Refresh the webpage after installing extension
- Check browser console for error messages
- Try disabling and re-enabling the extension

## Development

### Building from Source
1. Clone the repository
2. Navigate to `chrome-extension` folder
3. Load as unpacked extension in Chrome

### Making Changes
1. Edit source files in `src/` directory
2. Reload extension in `chrome://extensions/`
3. Test functionality on various websites

## Support

For issues, feature requests, or contributions, please refer to the main Smart Scribe project repository.

## License

This extension is part of the Smart Scribe project. Please refer to the main project for licensing information.
