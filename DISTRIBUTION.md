# Smart Scribe - Windows Distribution

## 📦 Installation Options

### Option 1: Installer (Recommended)
1. Download `Smart-Scribe-Setup.exe` from `out/make/squirrel.windows/x64/`
2. Run the installer as administrator
3. Follow the installation wizard
4. Smart Scribe will be installed and available in your Start Menu

### Option 2: Portable Version
1. Copy the entire `out/Smart Scribe-win32-x64/` folder to your desired location
2. Run `smart-scribe.exe` directly from the folder
3. No installation required - fully portable

## 🚀 Quick Start

### Using the Installer
- After installation, find "Smart Scribe" in your Start Menu
- Or use the desktop shortcut if created during installation

### Using the Portable Version
- Navigate to the `Smart Scribe-win32-x64` folder
- Double-click `smart-scribe.exe` to launch
- Or use the provided `run-smart-scribe.bat` script

## 📋 System Requirements

- **Operating System**: Windows 10 or later (64-bit)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 200MB free space
- **Internet**: Required for OpenRouter API (optional for local Ollama)

## 🔧 Setup Instructions

### For Ollama (Local AI)
1. Download and install Ollama from [ollama.ai](https://ollama.ai/)
2. Open Command Prompt and run: `ollama pull gemma:2b-instruct`
3. Start Ollama service: `ollama serve`
4. Launch Smart Scribe and select "Ollama (Local)" in settings

### For OpenRouter (Cloud AI)
1. Sign up at [openrouter.ai](https://openrouter.ai/)
2. Get your API key from [OpenRouter Dashboard](https://openrouter.ai/keys)
3. Launch Smart Scribe
4. Click the settings button (⚙️) or press Alt+S
5. Select "OpenRouter (Cloud)" and enter your API key

## 📁 File Structure

```
Smart Scribe-win32-x64/
├── smart-scribe.exe          # Main application
├── resources/                # Application resources
├── locales/                  # Language files
├── *.dll                     # Required libraries
└── LICENSE                   # License information
```

## 🆘 Troubleshooting

### Application Won't Start
- Ensure you have Windows 10 or later
- Try running as administrator
- Check Windows Defender/antivirus settings

### Ollama Connection Issues
- Verify Ollama is installed and running
- Check if `ollama serve` is active in Command Prompt
- Ensure no firewall is blocking localhost:11434

### OpenRouter API Issues
- Verify your API key is correct
- Check your internet connection
- Ensure you have credits in your OpenRouter account

## 📞 Support

For issues and support:
- GitHub: [https://github.com/abdelazizelm/smart-scribe](https://github.com/abdelazizelm/smart-scribe)
- Email: <EMAIL>

## 📄 License

MIT License - see LICENSE file for details.

---

**Smart Scribe v1.0.0**  
*AI-powered writing assistant with support for multiple AI providers*  
Copyright © 2024 Abdelaziz ELMEHAMMEDY
