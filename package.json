{"name": "smart-scribe", "productName": "Smart Scribe", "version": "1.0.0", "description": "AI-powered writing assistant with support for multiple AI providers", "main": "src/index.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": [], "author": {"name": "AbdelazizELMEHAMMEDY", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.1"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "electron": "36.4.0"}}