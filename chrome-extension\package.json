{"name": "smart-scribe-chrome-extension", "version": "1.0.0", "description": "AI-powered writing assistant browser extension with support for multiple AI providers", "author": "Abdelaziz ELMEHAMMEDY", "license": "MIT", "keywords": ["chrome-extension", "ai", "writing-assistant", "text-processing", "ollama", "openrouter", "grammar", "style-improvement"], "repository": {"type": "git", "url": "https://github.com/your-username/smart-scribe"}, "scripts": {"build": "echo 'Extension is ready for loading in Chrome'", "package": "zip -r smart-scribe-extension.zip . -x '*.DS_Store' 'node_modules/*' 'package.json' 'README.md'", "dev": "echo 'Load the extension folder in Chrome Developer Mode'"}, "devDependencies": {}, "dependencies": {}, "manifest": {"version": "1.0.0", "manifest_version": 3, "minimum_chrome_version": "88"}}