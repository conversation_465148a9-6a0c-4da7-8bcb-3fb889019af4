@echo off
echo Starting Smart Scribe...
echo.
echo Smart Scribe - AI Writing Assistant
echo Copyright (c) 2024 Abdelaziz ELMEHAMMEDY
echo.

REM Check if the executable exists
if exist "out\Smart Scribe-win32-x64\smart-scribe.exe" (
    echo Launching Smart Scribe...
    start "" "out\Smart Scribe-win32-x64\smart-scribe.exe"
) else (
    echo Error: Smart Scribe executable not found!
    echo Please run 'npm run make' first to build the application.
    echo.
    pause
)
