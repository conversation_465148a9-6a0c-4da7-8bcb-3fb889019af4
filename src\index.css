/* Modern UI Styles for <PERSON> Scribe */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --secondary-color: #6b7280;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;

  /* Neutral Colors - Simplified */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-focus: #2563eb;

  /* Modern Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-secondary);
  min-height: 100vh;
  overflow-x: hidden;
  padding: var(--spacing-4);
}

/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 2rem);
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid var(--border-light);
}

/* Header Styles - Simplified */
.app-header {
  background-color: var(--bg-primary);
  color: var(--gray-800);
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--border-light);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.logo-icon {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
  color: var(--gray-900);
}

.app-subtitle {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  font-weight: 500;
  margin-left: var(--spacing-2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background-color: var(--bg-tertiary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

.status-dot {
  width: 4px;
  height: 4px;
  background-color: var(--success-color);
  border-radius: 50%;
}

.status-text {
  font-size: 10px;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  background-color: var(--bg-secondary);
}

/* Quick Controls Section */
.quick-controls-section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.quick-controls-panel {
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-4);
}

.quick-controls-left {
  display: flex;
  gap: var(--spacing-3);
  flex: 1;
}

.quick-controls-right {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  min-width: 120px;
}

.control-label {
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.label-icon {
  font-size: var(--font-size-base);
}

/* Settings Button */
.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  background-color: var(--bg-tertiary);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 40px;
  height: 40px;
}

.settings-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
}

.settings-icon {
  font-size: var(--font-size-base);
}

.control-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  cursor: pointer;
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 14px;
  padding-right: var(--spacing-8);
}

.control-select:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.control-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Model Select Container */
.model-select-container {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
  width: 100%;
}

.model-select-container .setting-select {
  flex: 1;
  min-width: 0; /* Allow shrinking */
}

/* Model Actions */
.model-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
  flex-wrap: wrap;
}

.set-default-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-tertiary);
  color: var(--gray-700);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.set-default-btn:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.set-default-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.set-default-btn .button-icon {
  font-size: var(--font-size-sm);
}

.default-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.default-indicator .indicator-icon {
  font-size: var(--font-size-sm);
}

.refresh-models-btn {
  padding: var(--spacing-2);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.refresh-models-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
  transform: rotate(90deg);
}

.refresh-models-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.refresh-models-btn.loading {
  animation: spin 1s linear infinite;
}

/* API Key Section */
.api-key-section {
  padding: var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  margin-top: var(--spacing-4);
  transition: all var(--transition-normal);
}

.api-key-section.show {
  display: block !important;
  animation: slideDown var(--transition-normal) ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.api-key-container {
  position: relative;
  display: flex;
  align-items: center;
}

.api-key-input {
  flex: 1;
  padding: var(--spacing-3) var(--spacing-4);
  padding-right: var(--spacing-10);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
}

.api-key-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.api-key-input::placeholder {
  color: var(--gray-400);
  font-style: italic;
}

.toggle-visibility-btn {
  position: absolute;
  right: var(--spacing-2);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  font-size: var(--font-size-base);
}

.toggle-visibility-btn:hover {
  background-color: var(--gray-100);
}

.api-key-help {
  display: block;
  margin-top: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: 1.4;
}

.api-key-help a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.api-key-help a:hover {
  text-decoration: underline;
}

/* Settings Modal */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.settings-modal.active {
  opacity: 1;
  visibility: visible;
}

.settings-modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 95%;
  max-width: 550px;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.95) translateY(20px);
  transition: transform var(--transition-normal);
  border: 1px solid var(--border-light);
  margin: var(--spacing-4);
}

.settings-modal.active .settings-modal-content {
  transform: scale(1) translateY(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.settings-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
}

.settings-title-icon {
  font-size: var(--font-size-xl);
}

.close-settings-btn {
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-settings-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.settings-body {
  padding: var(--spacing-4);
  max-height: 70vh;
  overflow-y: auto;
}

/* Custom scrollbar for settings body */
.settings-body::-webkit-scrollbar {
  width: 6px;
}

.settings-body::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

.settings-body::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-sm);
}

.settings-body::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

.settings-section {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-3);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-light);
}

.section-icon {
  font-size: var(--font-size-xl);
}

.settings-grid {
  display: grid;
  gap: var(--spacing-5);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  width: 100%;
  min-width: 0; /* Allow shrinking */
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 600;
  font-size: var(--font-size-base);
  color: var(--gray-700);
}

.setting-select,
.setting-input {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
  width: 100%;
  box-sizing: border-box;
}

.setting-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 14px;
  padding-right: var(--spacing-6);
}

.setting-select:hover,
.setting-input:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.setting-description {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  line-height: 1.4;
}

.setting-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.setting-link:hover {
  text-decoration: underline;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-left: auto;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-slider:hover {
  background-color: var(--gray-400);
}

input:checked + .toggle-slider:hover {
  background-color: var(--primary-hover);
}

/* Settings Footer */
.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-5) var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.reset-settings-btn,
.save-settings-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.reset-settings-btn {
  background-color: var(--gray-200);
  color: var(--gray-700);
  margin-bottom: var(--spacing-3);
}

.reset-settings-btn:hover {
  background-color: var(--gray-300);
  color: var(--gray-800);
}

.save-settings-btn {
  background-color: var(--primary-color);
  color: white;
  margin-bottom: var(--spacing-3);
}

.save-settings-btn:hover {
  background-color: var(--primary-hover);
}

/* Refine Button - Simplified */
.refine-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  min-height: 44px;
}

.refine-button:hover {
  background-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
}

.refine-button:active {
  transform: translateY(1px);
}

.refine-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  font-size: var(--font-size-lg);
  transition: transform var(--transition-fast);
}

.button-text {
  font-weight: 600;
}

.button-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.refine-button.loading .button-loader {
  opacity: 1;
}

.refine-button.loading .button-icon,
.refine-button.loading .button-text {
  opacity: 0;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Text Section */
.text-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.text-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  flex: 1;
  min-height: 0;
}

.text-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: box-shadow var(--transition-fast);
}

.text-panel:hover {
  box-shadow: var(--shadow-md);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.panel-icon {
  font-size: var(--font-size-lg);
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
}

.copy-button.copied {
  background-color: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.char-counter {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  font-weight: 500;
}

/* Textarea Container */
.textarea-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

textarea {
  flex: 1;
  padding: var(--spacing-4);
  border: none;
  border-radius: 0;
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  resize: none;
  outline: none;
  transition: all var(--transition-fast);
  min-height: 280px;
}

textarea::placeholder {
  color: var(--gray-400);
}

textarea:focus {
  background-color: var(--bg-secondary);
}

#input-text {
  border-left: 3px solid var(--primary-color);
}

#output-text {
  border-left: 3px solid var(--success-color);
  background-color: var(--bg-secondary);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(4px);
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--gray-600);
  text-align: center;
}

/* Input Panel Specific Styles */
.input-panel {
  border-top: 2px solid var(--primary-color);
}

/* Output Panel Specific Styles */
.output-panel {
  border-top: 2px solid var(--success-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    padding: var(--spacing-6);
    gap: var(--spacing-6);
  }

  .quick-controls-panel {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .quick-controls-left {
    width: 100%;
    justify-content: space-between;
  }

  .quick-controls-right {
    width: 100%;
    justify-content: center;
  }

  .text-panels {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .app-header {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  .settings-modal-content {
    width: 98%;
    max-width: 500px;
    max-height: 85vh;
    margin: var(--spacing-2);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-4);
    gap: var(--spacing-4);
  }

  .quick-controls-left {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .control-group {
    min-width: auto;
  }

  .panel-header {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .panel-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  textarea {
    padding: var(--spacing-4);
    min-height: 200px;
  }

  .app-container {
    box-shadow: none;
  }

  .settings-modal-content {
    width: 100%;
    max-width: none;
    max-height: 95vh;
    margin: 0;
    border-radius: 0;
  }

  .settings-header {
    padding: var(--spacing-4);
  }

  .settings-body {
    padding: var(--spacing-3);
  }

  .model-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .set-default-btn {
    width: 100%;
    justify-content: center;
  }

  .settings-footer {
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-4) var(--spacing-3);
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  body {
    padding: var(--spacing-2);
  }

  .app-header {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .main-content {
    padding: var(--spacing-3);
    gap: var(--spacing-3);
  }

  .quick-controls-panel {
    padding: var(--spacing-3);
  }

  .refine-button {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
    min-height: 40px;
  }

  .app-title {
    font-size: var(--font-size-xl);
  }

  .app-subtitle {
    display: none;
  }

  .app-container {
    border-radius: var(--radius-lg);
    min-height: calc(100vh - 1rem);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Better Accessibility */
button:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --gray-400: #000000;
    --gray-500: #000000;
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;

    --border-light: #334155;
    --border-medium: #475569;
  }

  body {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  }
}