/* Modern UI Styles for <PERSON> Scribe */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Palette */
  --primary-color: #6366f1;
  --primary-hover: #5b5bd6;
  --primary-light: #e0e7ff;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  /* Border Colors */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-focus: #6366f1;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-light) 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-xl);
}

/* Header Styles */
.app-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  padding: var(--spacing-6) var(--spacing-8);
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.logo-icon {
  font-size: var(--font-size-3xl);
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(5deg); }
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.app-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  font-weight: 400;
  margin-left: var(--spacing-2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background-color: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: var(--success-color);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-8);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  background-color: var(--bg-secondary);
}

/* Quick Controls Section */
.quick-controls-section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.quick-controls-panel {
  padding: var(--spacing-5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-6);
}

.quick-controls-left {
  display: flex;
  gap: var(--spacing-4);
  flex: 1;
}

.quick-controls-right {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  min-width: 140px;
}

.control-label {
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.label-icon {
  font-size: var(--font-size-base);
}

/* Settings Button */
.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 48px;
  height: 48px;
}

.settings-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
  transform: rotate(45deg);
}

.settings-icon {
  font-size: var(--font-size-lg);
}

.control-select {
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  cursor: pointer;
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: var(--spacing-10);
}

.control-select:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.control-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Model Select Container */
.model-select-container {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.model-select-container .control-select {
  flex: 1;
}

.refresh-models-btn {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 40px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-models-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
  transform: rotate(90deg);
}

.refresh-models-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.refresh-models-btn.loading {
  animation: spin 1s linear infinite;
}

/* API Key Section */
.api-key-section {
  padding: var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  margin-top: var(--spacing-4);
  transition: all var(--transition-normal);
}

.api-key-section.show {
  display: block !important;
  animation: slideDown var(--transition-normal) ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.api-key-container {
  position: relative;
  display: flex;
  align-items: center;
}

.api-key-input {
  flex: 1;
  padding: var(--spacing-3) var(--spacing-4);
  padding-right: var(--spacing-10);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
}

.api-key-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.api-key-input::placeholder {
  color: var(--gray-400);
  font-style: italic;
}

.toggle-visibility-btn {
  position: absolute;
  right: var(--spacing-2);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  font-size: var(--font-size-base);
}

.toggle-visibility-btn:hover {
  background-color: var(--gray-100);
}

.api-key-help {
  display: block;
  margin-top: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: 1.4;
}

.api-key-help a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.api-key-help a:hover {
  text-decoration: underline;
}

/* Settings Modal */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.settings-modal.active {
  opacity: 1;
  visibility: visible;
}

.settings-modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-normal);
}

.settings-modal.active .settings-modal-content {
  transform: scale(1) translateY(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
}

.settings-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.settings-title-icon {
  font-size: var(--font-size-2xl);
}

.close-settings-btn {
  background: none;
  border: none;
  color: white;
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-settings-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.settings-body {
  padding: var(--spacing-6);
  max-height: 60vh;
  overflow-y: auto;
}

.settings-section {
  margin-bottom: var(--spacing-8);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: 2px solid var(--border-light);
}

.section-icon {
  font-size: var(--font-size-xl);
}

.settings-grid {
  display: grid;
  gap: var(--spacing-5);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 600;
  font-size: var(--font-size-base);
  color: var(--gray-700);
}

.setting-select,
.setting-input {
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
}

.setting-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: var(--spacing-10);
}

.setting-select:hover,
.setting-input:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.setting-description {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  line-height: 1.4;
}

.setting-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.setting-link:hover {
  text-decoration: underline;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-left: auto;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-slider:hover {
  background-color: var(--gray-400);
}

input:checked + .toggle-slider:hover {
  background-color: var(--primary-hover);
}

/* Settings Footer */
.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-5) var(--spacing-6);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.reset-settings-btn,
.save-settings-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.reset-settings-btn {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.reset-settings-btn:hover {
  background-color: var(--gray-300);
  color: var(--gray-800);
}

.save-settings-btn {
  background-color: var(--primary-color);
  color: white;
}

.save-settings-btn:hover {
  background-color: var(--primary-hover);
}

/* Refine Button */
.refine-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-8);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  min-height: 56px;
}

.refine-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.refine-button:active {
  transform: translateY(0);
}

.refine-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  font-size: var(--font-size-xl);
  transition: transform var(--transition-fast);
}

.refine-button:hover .button-icon {
  transform: scale(1.1);
}

.button-text {
  font-weight: 600;
}

.button-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.refine-button.loading .button-loader {
  opacity: 1;
}

.refine-button.loading .button-icon,
.refine-button.loading .button-text {
  opacity: 0;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Text Section */
.text-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.text-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  flex: 1;
  min-height: 0;
}

.text-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: box-shadow var(--transition-normal);
}

.text-panel:hover {
  box-shadow: var(--shadow-lg);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-5) var(--spacing-6);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.panel-icon {
  font-size: var(--font-size-xl);
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
}

.copy-button.copied {
  background-color: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.char-counter {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  font-weight: 500;
}

/* Textarea Container */
.textarea-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

textarea {
  flex: 1;
  padding: var(--spacing-5);
  border: none;
  border-radius: 0;
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  resize: none;
  outline: none;
  transition: all var(--transition-fast);
  min-height: 300px;
}

textarea::placeholder {
  color: var(--gray-400);
  font-style: italic;
}

textarea:focus {
  background-color: var(--bg-tertiary);
}

#input-text {
  border-left: 4px solid var(--primary-color);
}

#output-text {
  border-left: 4px solid var(--success-color);
  background-color: var(--bg-tertiary);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(4px);
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--gray-600);
  text-align: center;
}

/* Input Panel Specific Styles */
.input-panel {
  border-top: 3px solid var(--primary-color);
}

.input-panel .panel-header {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-tertiary) 100%);
}

/* Output Panel Specific Styles */
.output-panel {
  border-top: 3px solid var(--success-color);
}

.output-panel .panel-header {
  background: linear-gradient(135deg, #ecfdf5 0%, var(--bg-tertiary) 100%);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    padding: var(--spacing-6);
    gap: var(--spacing-6);
  }

  .quick-controls-panel {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .quick-controls-left {
    width: 100%;
    justify-content: space-between;
  }

  .quick-controls-right {
    width: 100%;
    justify-content: center;
  }

  .text-panels {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .app-header {
    padding: var(--spacing-4) var(--spacing-6);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  .settings-modal-content {
    width: 95%;
    max-height: 90vh;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-4);
    gap: var(--spacing-4);
  }

  .quick-controls-left {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .control-group {
    min-width: auto;
  }

  .panel-header {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .panel-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  textarea {
    padding: var(--spacing-4);
    min-height: 200px;
  }

  .app-container {
    box-shadow: none;
  }

  .settings-modal-content {
    width: 98%;
    max-height: 95vh;
  }

  .settings-header {
    padding: var(--spacing-4);
  }

  .settings-body {
    padding: var(--spacing-4);
  }

  .settings-footer {
    padding: var(--spacing-4);
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .main-content {
    padding: var(--spacing-3);
    gap: var(--spacing-3);
  }

  .controls-panel {
    padding: var(--spacing-3);
  }

  .refine-button {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
  }

  .app-title {
    font-size: var(--font-size-xl);
  }

  .app-subtitle {
    display: none;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Better Accessibility */
button:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --gray-400: #000000;
    --gray-500: #000000;
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;

    --border-light: #334155;
    --border-medium: #475569;
  }

  body {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  }
}