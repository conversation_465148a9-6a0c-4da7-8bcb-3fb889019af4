<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smart Scribe - AI Writing Assistant</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="extension-container">
    <!-- Header -->
    <header class="extension-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">✍️</div>
          <h1 class="app-title">Smart Scribe</h1>
        </div>
        <div class="header-actions">
          <button id="settings-btn" class="settings-button" title="Settings">
            <span class="settings-icon">⚙</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Quick Controls -->
    <section class="controls-section">
      <div class="controls-grid">
        <div class="control-group">
          <label for="action-select" class="control-label">Action</label>
          <select id="action-select" class="control-select">
            <option value="improve-style">Improve Style</option>
            <option value="fix-grammar">Fix Grammar</option>
            <option value="shorten">Shorten</option>
            <option value="expand">Expand</option>
          </select>
        </div>

        <div class="control-group">
          <label for="tone-select" class="control-label">Tone</label>
          <select id="tone-select" class="control-select">
            <option value="professional">Professional</option>
            <option value="casual">Casual</option>
            <option value="confident">Confident</option>
            <option value="friendly">Friendly</option>
          </select>
        </div>

        <div class="control-group">
          <label for="lang-select" class="control-label">Language</label>
          <select id="lang-select" class="control-select">
            <option value="English">English</option>
            <option value="French">French</option>
            <option value="Spanish">Spanish</option>
            <option value="German">German</option>
          </select>
        </div>
      </div>
    </section>

    <!-- Text Areas -->
    <section class="text-section">
      <div class="text-panel input-panel">
        <div class="panel-header">
          <h3 class="panel-title">Input</h3>
          <button id="clear-btn" class="action-button" title="Clear text">Clear</button>
        </div>
        <textarea id="input-text" placeholder="Type or paste your text here..." rows="4"></textarea>
      </div>

      <div class="text-panel output-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <span id="ai-suggestion-title">AI Output</span>
          </h3>
          <button id="copy-btn" class="action-button copy-button" title="Copy to clipboard">Copy</button>
        </div>
        <textarea id="output-text" readonly placeholder="AI refined text will appear here..." rows="4"></textarea>
        <div class="loading-overlay">
          <div class="loading-spinner"></div>
          <p id="loading-text" class="loading-text">AI is thinking...</p>
        </div>
      </div>
    </section>

    <!-- Action Button -->
    <section class="action-section">
      <button id="refine-btn" class="refine-button">
        <span class="button-icon">✨</span>
        <span class="button-text">Refine Text</span>
        <div class="button-loader"></div>
      </button>
    </section>
  </div>

  <!-- Settings Modal -->
  <div id="settings-modal" class="settings-modal">
    <div class="settings-modal-content">
      <div class="settings-header">
        <h2 class="settings-title">
          <span class="settings-title-icon">⚙️</span>
          Settings
        </h2>
        <button id="close-settings-btn" class="close-settings-btn" title="Close settings">✕</button>
      </div>

      <div class="settings-body">
        <!-- AI Provider Settings -->
        <div class="settings-section">
          <h3 class="settings-section-title">
            <span class="section-icon">🤖</span>
            AI Provider
          </h3>

          <div class="setting-item">
            <label for="provider-select" class="setting-label">
              <span class="label-icon">🔌</span>
              Provider
            </label>
            <select id="provider-select" class="setting-select">
              <option value="ollama">Ollama (Local)</option>
              <option value="openrouter">OpenRouter (Cloud)</option>
            </select>
            <small class="setting-description">Choose between local Ollama or cloud OpenRouter</small>
          </div>

          <div class="setting-item">
            <label for="model-select" class="setting-label">
              <span class="label-icon">🧠</span>
              Model
            </label>
            <div class="model-select-container">
              <select id="model-select" class="setting-select">
                <option value="">Loading models...</option>
              </select>
              <button type="button" id="refresh-models-btn" class="refresh-models-btn" title="Refresh models">🔄</button>
            </div>
            <div class="model-actions">
              <button type="button" id="set-default-model-btn" class="set-default-btn" title="Set as default model">
                <span class="button-icon">⭐</span>
                <span class="button-text">Set as Default</span>
              </button>
              <span id="default-model-indicator" class="default-indicator" style="display: none;">
                <span class="indicator-icon">⭐</span>
                <span class="indicator-text">Default Model</span>
              </span>
            </div>
            <small class="setting-description">Select the AI model to use for text refinement</small>
          </div>
        </div>

        <!-- OpenRouter API Settings -->
        <div id="openrouter-settings" class="settings-section" style="display: none;">
          <h3 class="settings-section-title">
            <span class="section-icon">🔑</span>
            OpenRouter API
          </h3>

          <div class="setting-item">
            <label for="api-key-input" class="setting-label">
              <span class="label-icon">🔐</span>
              API Key
            </label>
            <div class="api-key-container">
              <input type="password" id="api-key-input" class="setting-input"
                placeholder="Enter your OpenRouter API key..." autocomplete="off" />
              <button type="button" id="toggle-key-visibility" class="toggle-visibility-btn" title="Toggle visibility">👁️</button>
            </div>
            <small class="setting-description">
              Get your API key from <a href="https://openrouter.ai/keys" target="_blank" rel="noopener" class="setting-link">OpenRouter Dashboard</a>
            </small>
          </div>
        </div>
      </div>

      <div class="settings-footer">
        <button id="reset-settings-btn" class="reset-settings-btn">
          <span class="button-icon">🔄</span>
          Reset to Defaults
        </button>
        <button id="save-settings-btn" class="save-settings-btn">
          <span class="button-icon">💾</span>
          Save Settings
        </button>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
