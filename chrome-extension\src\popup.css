/* Extension-specific CSS Variables */
:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --secondary-color: #6b7280;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-focus: #2563eb;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  width: 400px;
  min-height: 600px;
  overflow-x: hidden;
}

/* Extension Container */
.extension-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-primary);
}

/* Header */
.extension-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.logo-icon {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
  color: var(--gray-900);
}

.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  background-color: var(--bg-tertiary);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
}

.settings-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

/* Controls Section */
.controls-section {
  padding: var(--spacing-3);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.controls-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-2);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.control-label {
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-select {
  padding: var(--spacing-2);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  cursor: pointer;
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-1) center;
  background-repeat: no-repeat;
  background-size: 12px;
  padding-right: var(--spacing-5);
}

.control-select:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.control-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Text Section */
.text-section {
  flex: 1;
  padding: var(--spacing-3);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.text-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.panel-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.action-button {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--bg-primary);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-button:hover {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

textarea {
  padding: var(--spacing-3);
  border: none;
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
  line-height: 1.5;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  resize: none;
  outline: none;
  transition: all var(--transition-fast);
}

textarea::placeholder {
  color: var(--gray-400);
}

textarea:focus {
  background-color: var(--bg-secondary);
}

.input-panel {
  border-left: 3px solid var(--primary-color);
}

.output-panel {
  border-left: 3px solid var(--success-color);
  position: relative;
}

#output-text {
  background-color: var(--bg-secondary);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.loading-overlay.active {
  display: flex;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  font-weight: 500;
}

/* Action Section */
.action-section {
  padding: var(--spacing-3);
  border-top: 1px solid var(--border-light);
}

.refine-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  width: 100%;
  padding: var(--spacing-3);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.refine-button:hover {
  background-color: var(--primary-hover);
}

.refine-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-icon {
  font-size: var(--font-size-base);
}

.button-loader {
  display: none;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  right: var(--spacing-3);
}

.refine-button.loading .button-loader {
  display: block;
}

.refine-button.loading .button-text,
.refine-button.loading .button-icon {
  opacity: 0.7;
}

/* Settings Modal */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.settings-modal.active {
  display: flex;
}

.settings-modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 95%;
  max-width: 380px;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.95) translateY(20px);
  transition: transform var(--transition-normal);
  border: 1px solid var(--border-light);
}

.settings-modal.active .settings-modal-content {
  transform: scale(1) translateY(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.settings-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-800);
}

.close-settings-btn {
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: var(--font-size-base);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-settings-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.settings-body {
  padding: var(--spacing-3);
  max-height: 60vh;
  overflow-y: auto;
}

.settings-section {
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-3);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
  padding-bottom: var(--spacing-1);
  border-bottom: 1px solid var(--border-light);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-3);
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--gray-700);
}

.setting-select,
.setting-input {
  padding: var(--spacing-2);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
  width: 100%;
}

.setting-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-1) center;
  background-repeat: no-repeat;
  background-size: 12px;
  padding-right: var(--spacing-5);
}

.setting-select:hover,
.setting-input:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.setting-description {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: 1.4;
}

.model-select-container {
  display: flex;
  gap: var(--spacing-1);
  align-items: center;
}

.model-select-container .setting-select {
  flex: 1;
}

.refresh-models-btn {
  padding: var(--spacing-1);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.refresh-models-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.model-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-1);
  flex-wrap: wrap;
}

.set-default-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--bg-tertiary);
  color: var(--gray-700);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.set-default-btn:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.default-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.api-key-container {
  display: flex;
  gap: var(--spacing-1);
  align-items: center;
}

.api-key-container .setting-input {
  flex: 1;
}

.toggle-visibility-btn {
  padding: var(--spacing-1);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.toggle-visibility-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.setting-link {
  color: var(--primary-color);
  text-decoration: none;
}

.setting-link:hover {
  text-decoration: underline;
}

.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3) var(--spacing-3) var(--spacing-3);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
  gap: var(--spacing-2);
}

.reset-settings-btn,
.save-settings-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  flex: 1;
  justify-content: center;
}

.reset-settings-btn {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.reset-settings-btn:hover {
  background-color: var(--gray-300);
  color: var(--gray-800);
}

.save-settings-btn {
  background-color: var(--primary-color);
  color: white;
}

.save-settings-btn:hover {
  background-color: var(--primary-hover);
}
