// Extension Popup JavaScript
console.log('Smart Scribe Extension Popup Loaded');

// DOM Elements
const inputText = document.getElementById('input-text');
const outputText = document.getElementById('output-text');
const refineBtn = document.getElementById('refine-btn');
const clearBtn = document.getElementById('clear-btn');
const copyBtn = document.getElementById('copy-btn');
const settingsBtn = document.getElementById('settings-btn');
const settingsModal = document.getElementById('settings-modal');
const closeSettingsBtn = document.getElementById('close-settings-btn');

// Control elements
const actionSelect = document.getElementById('action-select');
const toneSelect = document.getElementById('tone-select');
const langSelect = document.getElementById('lang-select');

// Settings elements
const providerSelect = document.getElementById('provider-select');
const modelSelect = document.getElementById('model-select');
const refreshModelsBtn = document.getElementById('refresh-models-btn');
const apiKeyInput = document.getElementById('api-key-input');
const toggleKeyVisibilityBtn = document.getElementById('toggle-key-visibility');
const saveSettingsBtn = document.getElementById('save-settings-btn');
const resetSettingsBtn = document.getElementById('reset-settings-btn');

// Model default elements
const setDefaultModelBtn = document.getElementById('set-default-model-btn');
const defaultModelIndicator = document.getElementById('default-model-indicator');

// Dynamic UI elements
const aiSuggestionTitle = document.getElementById('ai-suggestion-title');
const loadingText = document.getElementById('loading-text');
const loadingOverlay = document.querySelector('.loading-overlay');

// OpenRouter settings section
const openrouterSettings = document.getElementById('openrouter-settings');

// State
let isProcessing = false;

// Fallback models for when API is unavailable
const FALLBACK_MODELS = {
    ollama: [
        { id: 'llama3.2:3b', name: 'Llama 3.2 3B' },
        { id: 'gemma2:2b', name: 'Gemma 2 2B' },
        { id: 'qwen2.5:3b', name: 'Qwen 2.5 3B' }
    ],
    openrouter: [
        { id: 'meta-llama/llama-3.2-3b-instruct:free', name: 'Llama 3.2 3B Instruct (Free)' },
        { id: 'microsoft/phi-3-mini-128k-instruct:free', name: 'Phi-3 Mini 128K (Free)' },
        { id: 'google/gemma-2-9b-it:free', name: 'Gemma 2 9B IT (Free)' }
    ]
};

// Initialize extension
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Extension popup initialized');
    
    // Load settings
    await loadSettings();
    
    // Initialize model options
    await updateModelOptions();
    
    // Update dynamic UI elements
    updateAllDynamicElements();
    
    // Focus on input
    inputText.focus();
});

// Event Listeners
refineBtn.addEventListener('click', handleRefineText);
clearBtn.addEventListener('click', () => {
    inputText.value = '';
    outputText.value = '';
    inputText.focus();
});

copyBtn.addEventListener('click', async () => {
    if (outputText.value) {
        try {
            await navigator.clipboard.writeText(outputText.value);
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 1500);
        } catch (err) {
            console.error('Failed to copy text:', err);
        }
    }
});

// Settings modal
settingsBtn.addEventListener('click', () => {
    settingsModal.classList.add('active');
});

closeSettingsBtn.addEventListener('click', () => {
    settingsModal.classList.remove('active');
});

settingsModal.addEventListener('click', (e) => {
    if (e.target === settingsModal) {
        settingsModal.classList.remove('active');
    }
});

// Provider change
providerSelect.addEventListener('change', () => {
    updateModelOptions();
    toggleOpenRouterSettings();
    saveSettings();
});

// Model change
modelSelect.addEventListener('change', () => {
    updateAllDynamicElements();
    saveSettings();
});

// Control changes
actionSelect.addEventListener('change', saveSettings);
toneSelect.addEventListener('change', saveSettings);
langSelect.addEventListener('change', saveSettings);

// Settings buttons
saveSettingsBtn.addEventListener('click', () => {
    saveSettings();
    settingsModal.classList.remove('active');
});

resetSettingsBtn.addEventListener('click', () => {
    if (confirm('Reset all settings to defaults?')) {
        resetSettings();
    }
});

refreshModelsBtn.addEventListener('click', updateModelOptions);

// API key visibility toggle
toggleKeyVisibilityBtn.addEventListener('click', () => {
    const isPassword = apiKeyInput.type === 'password';
    apiKeyInput.type = isPassword ? 'text' : 'password';
    toggleKeyVisibilityBtn.textContent = isPassword ? '🙈' : '👁️';
});

// Set default model
setDefaultModelBtn.addEventListener('click', setDefaultModel);

// Auto-save API key
apiKeyInput.addEventListener('input', saveSettings);

// Functions
function formatModelName(modelId) {
    return modelId
        .replace(/[_-]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
        .replace(/(\d+)([a-zA-Z])/g, '$1 $2')
        .replace(/\s+/g, ' ')
        .trim();
}

function getCurrentModelDisplayName() {
    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
    if (selectedOption && selectedOption.value) {
        return selectedOption.textContent;
    }
    return 'AI';
}

function getShortModelName(fullName) {
    const name = fullName.toLowerCase();
    
    if (name.includes('gemma')) return 'Gemma';
    if (name.includes('llama')) return 'Llama';
    if (name.includes('mistral')) return 'Mistral';
    if (name.includes('claude')) return 'Claude';
    if (name.includes('gpt')) return 'GPT';
    if (name.includes('qwen')) return 'Qwen';
    if (name.includes('phi')) return 'Phi';
    
    const words = fullName.split(' ');
    for (const word of words) {
        if (word.length > 2 && !word.match(/^\d+$/)) {
            return word;
        }
    }
    
    return 'AI';
}

function updateAISuggestionTitle() {
    const modelName = getCurrentModelDisplayName();
    const shortName = getShortModelName(modelName);
    aiSuggestionTitle.textContent = `${shortName} Output`;
}

function updateLoadingText() {
    const modelName = getCurrentModelDisplayName();
    const shortName = getShortModelName(modelName);
    loadingText.textContent = `${shortName} is thinking...`;
}

function updateDefaultModelIndicator() {
    chrome.storage.sync.get(['default_ai_provider', 'default_ai_model'], (result) => {
        const defaultProvider = result.default_ai_provider;
        const defaultModel = result.default_ai_model;
        const currentProvider = providerSelect.value;
        const currentModel = modelSelect.value;
        
        const isDefault = (defaultProvider === currentProvider && defaultModel === currentModel);
        
        if (isDefault && currentModel) {
            defaultModelIndicator.style.display = 'flex';
            setDefaultModelBtn.style.display = 'none';
        } else {
            defaultModelIndicator.style.display = 'none';
            setDefaultModelBtn.style.display = currentModel ? 'flex' : 'none';
        }
    });
}

function updateAllDynamicElements() {
    updateAISuggestionTitle();
    updateLoadingText();
    updateDefaultModelIndicator();
}

function toggleOpenRouterSettings() {
    const isOpenRouter = providerSelect.value === 'openrouter';
    openrouterSettings.style.display = isOpenRouter ? 'block' : 'none';
}

function setDefaultModel() {
    const selectedModel = modelSelect.value;
    const selectedProvider = providerSelect.value;
    
    if (!selectedModel) {
        alert('Please select a model first.');
        return;
    }
    
    chrome.storage.sync.set({
        'default_ai_provider': selectedProvider,
        'default_ai_model': selectedModel
    }, () => {
        updateDefaultModelIndicator();
        
        const originalText = setDefaultModelBtn.querySelector('.button-text').textContent;
        setDefaultModelBtn.querySelector('.button-text').textContent = 'Set!';
        setDefaultModelBtn.disabled = true;
        
        setTimeout(() => {
            setDefaultModelBtn.querySelector('.button-text').textContent = originalText;
            setDefaultModelBtn.disabled = false;
        }, 1500);
    });
}

// Settings management
async function loadSettings() {
    return new Promise((resolve) => {
        chrome.storage.sync.get([
            'ai_provider', 'ai_model', 'language', 'tone', 'action', 'openrouter_api_key'
        ], (result) => {
            providerSelect.value = result.ai_provider || 'ollama';
            langSelect.value = result.language || 'English';
            toneSelect.value = result.tone || 'professional';
            actionSelect.value = result.action || 'improve-style';
            apiKeyInput.value = result.openrouter_api_key || '';

            if (result.ai_model) {
                modelSelect.value = result.ai_model;
            }

            toggleOpenRouterSettings();
            resolve();
        });
    });
}

function saveSettings() {
    const settings = {
        ai_provider: providerSelect.value,
        ai_model: modelSelect.value,
        language: langSelect.value,
        tone: toneSelect.value,
        action: actionSelect.value,
        openrouter_api_key: apiKeyInput.value
    };

    chrome.storage.sync.set(settings);
}

function resetSettings() {
    chrome.storage.sync.clear(() => {
        providerSelect.value = 'ollama';
        modelSelect.innerHTML = '<option value="">Loading models...</option>';
        langSelect.value = 'English';
        toneSelect.value = 'professional';
        actionSelect.value = 'improve-style';
        apiKeyInput.value = '';

        updateModelOptions();
        toggleOpenRouterSettings();
        updateAllDynamicElements();
    });
}

// Model management
async function updateModelOptions() {
    const provider = providerSelect.value;
    modelSelect.innerHTML = '<option value="">Loading models...</option>';
    modelSelect.disabled = true;

    try {
        let models = [];

        if (provider === 'ollama') {
            models = await fetchOllamaModels();
        } else if (provider === 'openrouter') {
            models = await fetchOpenRouterModels();
        }

        modelSelect.innerHTML = '';

        if (models.length > 0) {
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
        } else {
            const fallbackModels = FALLBACK_MODELS[provider] || [];
            fallbackModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
        }

        modelSelect.disabled = false;
        updateAllDynamicElements();

    } catch (error) {
        console.error('Error updating model options:', error);
        modelSelect.innerHTML = '<option value="">Error loading models</option>';
        modelSelect.disabled = false;
    }
}

async function fetchOllamaModels() {
    try {
        const response = await fetch('http://localhost:11434/api/tags');
        if (!response.ok) throw new Error('Ollama not available');

        const data = await response.json();
        return data.models.map(model => ({
            id: model.name,
            name: formatModelName(model.name)
        }));
    } catch (error) {
        console.error('Failed to fetch Ollama models:', error);
        return [];
    }
}

async function fetchOpenRouterModels() {
    try {
        const response = await fetch('https://openrouter.ai/api/v1/models', {
            headers: {
                'Authorization': `Bearer ${apiKeyInput.value}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) throw new Error('OpenRouter API error');

        const data = await response.json();
        return data.data
            .filter(model => !model.id.includes('vision') && !model.id.includes('embed'))
            .slice(0, 20)
            .map(model => ({
                id: model.id,
                name: model.name || formatModelName(model.id)
            }));
    } catch (error) {
        console.error('Failed to fetch OpenRouter models:', error);
        return [];
    }
}

// Text processing
async function handleRefineText() {
    const text = inputText.value.trim();
    if (!text) {
        alert('Please enter some text to refine.');
        return;
    }

    if (isProcessing) return;

    const selectedModel = modelSelect.value;
    if (!selectedModel) {
        alert('Please select an AI model first.');
        return;
    }

    isProcessing = true;
    setLoadingState(true);

    try {
        const provider = providerSelect.value;
        let result;

        if (provider === 'ollama') {
            result = await processWithOllama(text, selectedModel);
        } else if (provider === 'openrouter') {
            result = await processWithOpenRouter(text, selectedModel);
        }

        outputText.value = result;

    } catch (error) {
        console.error('Error processing text:', error);
        outputText.value = 'Error: ' + error.message;
    } finally {
        isProcessing = false;
        setLoadingState(false);
    }
}

function setLoadingState(loading) {
    loadingOverlay.classList.toggle('active', loading);
    refineBtn.classList.toggle('loading', loading);
    refineBtn.disabled = loading;
}

function createPrompt(text) {
    const action = actionSelect.value;
    const tone = toneSelect.value;
    const language = langSelect.value;

    const actionPrompts = {
        'improve-style': 'Improve the writing style and clarity of',
        'fix-grammar': 'Fix grammar, spelling, and punctuation errors in',
        'shorten': 'Make this text more concise and shorter',
        'expand': 'Expand and elaborate on'
    };

    const actionText = actionPrompts[action] || 'Improve';

    return `${actionText} the following text. Make it sound ${tone} and ensure it's in ${language}. Only return the improved text without any explanations or additional comments:\n\n${text}`;
}

async function processWithOllama(text, model) {
    const prompt = createPrompt(text);

    const response = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            model: model,
            prompt: prompt,
            stream: false,
            options: {
                temperature: 0.7,
                max_tokens: 1000
            }
        })
    });

    if (!response.ok) {
        throw new Error('Ollama API error: ' + response.statusText);
    }

    const data = await response.json();
    return data.response.trim();
}

async function processWithOpenRouter(text, model) {
    const apiKey = apiKeyInput.value;
    if (!apiKey) {
        throw new Error('OpenRouter API key is required');
    }

    const prompt = createPrompt(text);

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': chrome.runtime.getURL(''),
            'X-Title': 'Smart Scribe Extension'
        },
        body: JSON.stringify({
            model: model,
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.7,
            max_tokens: 1000
        })
    });

    if (!response.ok) {
        throw new Error('OpenRouter API error: ' + response.statusText);
    }

    const data = await response.json();
    return data.choices[0].message.content.trim();
}
