// Content Script for Smart Scribe Extension
console.log('Smart Scribe Content Script Loaded');

// State
let smartScribeWidget = null;
let currentTextArea = null;
let isProcessing = false;

// Initialize content script
function initializeContentScript() {
  // Listen for text selection
  document.addEventListener('mouseup', handleTextSelection);
  document.addEventListener('keyup', handleTextSelection);
  
  // Listen for focus on text areas and input fields
  document.addEventListener('focusin', handleTextFieldFocus);
  document.addEventListener('focusout', handleTextFieldBlur);
  
  console.log('Smart Scribe content script initialized');
}

// Handle text selection
function handleTextSelection() {
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();
  
  if (selectedText.length > 10) {
    showQuickActionButton(selection);
  } else {
    hideQuickActionButton();
  }
}

// Handle text field focus
function handleTextFieldFocus(event) {
  const element = event.target;
  
  // Check if it's a text input element
  if (isTextInputElement(element)) {
    currentTextArea = element;
    showTextFieldWidget(element);
  }
}

// Handle text field blur
function handleTextFieldBlur(event) {
  // Delay hiding to allow clicking on widget
  setTimeout(() => {
    if (!document.querySelector('.smart-scribe-widget:hover')) {
      hideTextFieldWidget();
      currentTextArea = null;
    }
  }, 200);
}

// Check if element is a text input
function isTextInputElement(element) {
  const tagName = element.tagName.toLowerCase();
  const type = element.type?.toLowerCase();
  
  return (
    tagName === 'textarea' ||
    (tagName === 'input' && ['text', 'email', 'search', 'url'].includes(type)) ||
    element.contentEditable === 'true' ||
    element.isContentEditable
  );
}

// Show quick action button for selected text
function showQuickActionButton(selection) {
  hideQuickActionButton();
  
  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();
  
  const button = document.createElement('div');
  button.id = 'smart-scribe-quick-action';
  button.className = 'smart-scribe-widget';
  button.innerHTML = `
    <div class="smart-scribe-button">
      <span class="icon">✨</span>
      <span class="text">Smart Scribe</span>
    </div>
  `;
  
  // Position the button
  button.style.cssText = `
    position: fixed;
    top: ${rect.bottom + window.scrollY + 10}px;
    left: ${rect.left + window.scrollX}px;
    z-index: 10000;
    background: #2563eb;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
  `;
  
  // Add hover effect
  button.addEventListener('mouseenter', () => {
    button.style.background = '#1d4ed8';
    button.style.transform = 'translateY(-2px)';
  });
  
  button.addEventListener('mouseleave', () => {
    button.style.background = '#2563eb';
    button.style.transform = 'translateY(0)';
  });
  
  // Handle click
  button.addEventListener('click', () => {
    showActionMenu(selection.toString(), rect);
    hideQuickActionButton();
  });
  
  document.body.appendChild(button);
}

// Hide quick action button
function hideQuickActionButton() {
  const existing = document.getElementById('smart-scribe-quick-action');
  if (existing) {
    existing.remove();
  }
}

// Show text field widget
function showTextFieldWidget(element) {
  hideTextFieldWidget();
  
  const rect = element.getBoundingClientRect();
  
  const widget = document.createElement('div');
  widget.id = 'smart-scribe-text-widget';
  widget.className = 'smart-scribe-widget';
  widget.innerHTML = `
    <div class="smart-scribe-text-controls">
      <button class="action-btn" data-action="improve-style" title="Improve Style">✨</button>
      <button class="action-btn" data-action="fix-grammar" title="Fix Grammar">📝</button>
      <button class="action-btn" data-action="shorten" title="Shorten">📏</button>
      <button class="action-btn" data-action="expand" title="Expand">📈</button>
      <button class="open-popup-btn" title="Open Smart Scribe">🚀</button>
    </div>
  `;
  
  // Position the widget
  widget.style.cssText = `
    position: fixed;
    top: ${rect.top + window.scrollY - 45}px;
    right: ${window.innerWidth - rect.right - window.scrollX + 10}px;
    z-index: 10000;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    gap: 4px;
  `;
  
  // Add button styles
  const style = document.createElement('style');
  style.textContent = `
    .smart-scribe-text-controls .action-btn,
    .smart-scribe-text-controls .open-popup-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 6px;
      background: #f3f4f6;
      color: #374151;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    
    .smart-scribe-text-controls .action-btn:hover,
    .smart-scribe-text-controls .open-popup-btn:hover {
      background: #2563eb;
      color: white;
      transform: translateY(-1px);
    }
    
    .smart-scribe-text-controls .open-popup-btn {
      background: #059669;
      color: white;
    }
    
    .smart-scribe-text-controls .open-popup-btn:hover {
      background: #047857;
    }
  `;
  
  if (!document.getElementById('smart-scribe-styles')) {
    style.id = 'smart-scribe-styles';
    document.head.appendChild(style);
  }
  
  // Add event listeners
  widget.querySelectorAll('.action-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      processTextFieldContent(element, action);
    });
  });
  
  widget.querySelector('.open-popup-btn').addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openPopup' });
  });
  
  document.body.appendChild(widget);
}

// Hide text field widget
function hideTextFieldWidget() {
  const existing = document.getElementById('smart-scribe-text-widget');
  if (existing) {
    existing.remove();
  }
}

// Show action menu for selected text
function showActionMenu(selectedText, rect) {
  const menu = document.createElement('div');
  menu.id = 'smart-scribe-action-menu';
  menu.className = 'smart-scribe-widget';
  menu.innerHTML = `
    <div class="smart-scribe-menu">
      <div class="menu-header">Smart Scribe Actions</div>
      <button class="menu-action" data-action="improve-style">✨ Improve Style</button>
      <button class="menu-action" data-action="fix-grammar">📝 Fix Grammar</button>
      <button class="menu-action" data-action="shorten">📏 Shorten</button>
      <button class="menu-action" data-action="expand">📈 Expand</button>
      <div class="menu-divider"></div>
      <button class="menu-action open-popup">🚀 Open Smart Scribe</button>
    </div>
  `;
  
  // Position the menu
  menu.style.cssText = `
    position: fixed;
    top: ${rect.bottom + window.scrollY + 10}px;
    left: ${rect.left + window.scrollX}px;
    z-index: 10000;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    overflow: hidden;
  `;
  
  // Add menu styles
  const menuStyle = document.createElement('style');
  menuStyle.textContent = `
    .smart-scribe-menu {
      padding: 8px;
    }
    
    .smart-scribe-menu .menu-header {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 12px;
      font-weight: 600;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 8px 12px 4px;
      margin-bottom: 4px;
    }
    
    .smart-scribe-menu .menu-action {
      width: 100%;
      padding: 10px 12px;
      border: none;
      background: none;
      text-align: left;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      color: #374151;
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .smart-scribe-menu .menu-action:hover {
      background: #f3f4f6;
      color: #2563eb;
    }
    
    .smart-scribe-menu .menu-divider {
      height: 1px;
      background: #e5e7eb;
      margin: 8px 0;
    }
    
    .smart-scribe-menu .open-popup {
      color: #059669;
    }
    
    .smart-scribe-menu .open-popup:hover {
      background: #ecfdf5;
      color: #047857;
    }
  `;
  
  if (!document.getElementById('smart-scribe-menu-styles')) {
    menuStyle.id = 'smart-scribe-menu-styles';
    document.head.appendChild(menuStyle);
  }
  
  // Add event listeners
  menu.querySelectorAll('.menu-action:not(.open-popup)').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      processSelectedText(selectedText, action);
      hideActionMenu();
    });
  });
  
  menu.querySelector('.open-popup').addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openPopup' });
    hideActionMenu();
  });
  
  // Close menu when clicking outside
  setTimeout(() => {
    document.addEventListener('click', function closeMenu(e) {
      if (!menu.contains(e.target)) {
        hideActionMenu();
        document.removeEventListener('click', closeMenu);
      }
    });
  }, 100);
  
  document.body.appendChild(menu);
}

// Hide action menu
function hideActionMenu() {
  const existing = document.getElementById('smart-scribe-action-menu');
  if (existing) {
    existing.remove();
  }
}

// Process selected text
async function processSelectedText(text, action) {
  if (isProcessing) return;
  
  isProcessing = true;
  showProcessingIndicator('Processing selected text...');
  
  try {
    // Get settings from storage
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get([
        'ai_provider', 'ai_model', 'language', 'tone', 'openrouter_api_key'
      ], resolve);
    });
    
    if (!settings.ai_model) {
      throw new Error('Please select an AI model in the extension settings first.');
    }
    
    // Send message to background script
    const response = await new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: 'processText',
        text: text,
        actionType: action,
        settings: settings
      }, resolve);
    });
    
    if (response.success) {
      replaceSelectedText(response.result);
      showSuccessIndicator('Text refined successfully!');
    } else {
      throw new Error(response.error);
    }
    
  } catch (error) {
    console.error('Error processing text:', error);
    showErrorIndicator('Error: ' + error.message);
  } finally {
    isProcessing = false;
    hideProcessingIndicator();
  }
}

// Process text field content
async function processTextFieldContent(element, action) {
  const text = getTextFromElement(element);
  if (!text.trim()) {
    showErrorIndicator('No text to process');
    return;
  }
  
  if (isProcessing) return;
  
  isProcessing = true;
  showProcessingIndicator('Processing text...');
  
  try {
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get([
        'ai_provider', 'ai_model', 'language', 'tone', 'openrouter_api_key'
      ], resolve);
    });
    
    if (!settings.ai_model) {
      throw new Error('Please select an AI model in the extension settings first.');
    }
    
    const response = await new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: 'processText',
        text: text,
        actionType: action,
        settings: settings
      }, resolve);
    });
    
    if (response.success) {
      setTextToElement(element, response.result);
      showSuccessIndicator('Text refined successfully!');
    } else {
      throw new Error(response.error);
    }
    
  } catch (error) {
    console.error('Error processing text:', error);
    showErrorIndicator('Error: ' + error.message);
  } finally {
    isProcessing = false;
    hideProcessingIndicator();
  }
}

// Utility functions
function getTextFromElement(element) {
  if (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input') {
    return element.value;
  } else if (element.contentEditable === 'true' || element.isContentEditable) {
    return element.textContent || element.innerText;
  }
  return '';
}

function setTextToElement(element, text) {
  if (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input') {
    element.value = text;
    element.dispatchEvent(new Event('input', { bubbles: true }));
  } else if (element.contentEditable === 'true' || element.isContentEditable) {
    element.textContent = text;
    element.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

function replaceSelectedText(newText) {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    range.deleteContents();
    range.insertNode(document.createTextNode(newText));
    selection.removeAllRanges();
  }
}

function showProcessingIndicator(message) {
  hideProcessingIndicator();
  
  const indicator = document.createElement('div');
  indicator.id = 'smart-scribe-processing';
  indicator.innerHTML = `
    <div style="display: flex; align-items: center; gap: 8px;">
      <div style="width: 16px; height: 16px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
      ${message}
    </div>
  `;
  
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2563eb;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
  `;
  
  document.body.appendChild(indicator);
}

function hideProcessingIndicator() {
  const existing = document.getElementById('smart-scribe-processing');
  if (existing) {
    existing.remove();
  }
}

function showSuccessIndicator(message) {
  const indicator = document.createElement('div');
  indicator.textContent = '✓ ' + message;
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #059669;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
  `;
  
  document.body.appendChild(indicator);
  
  setTimeout(() => {
    indicator.remove();
  }, 3000);
}

function showErrorIndicator(message) {
  const indicator = document.createElement('div');
  indicator.textContent = '⚠ ' + message;
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc2626;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
  `;
  
  document.body.appendChild(indicator);
  
  setTimeout(() => {
    indicator.remove();
  }, 4000);
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}
