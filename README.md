# ✨ Smart Scribe - AI Writing Assistant

<div align="center">

![Smart Scribe Logo](https://img.shields.io/badge/✨-Smart%20Scribe-6366f1?style=for-the-badge&labelColor=1e293b)

**A beautiful, modern desktop application for AI-powered text refinement using Gemma AI**

[![Electron](https://img.shields.io/badge/Electron-191970?style=flat&logo=Electron&logoColor=white)](https://www.electronjs.org/)
[![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=flat&logo=javascript&logoColor=black)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
[![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=flat&logo=css3&logoColor=white)](https://developer.mozilla.org/en-US/docs/Web/CSS)
[![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=flat&logo=html5&logoColor=white)](https://developer.mozilla.org/en-US/docs/Web/HTML)

</div>

## 🎯 Overview

Smart Scribe is a sleek desktop application that leverages the power of Gemma AI to help you refine, improve, and perfect your writing. Whether you need to fix grammar, adjust tone, improve style, or expand your content, Smart Scribe provides an intuitive interface for all your text enhancement needs.

## ✨ Features

### 🎨 **Modern UI/UX**
- **Clean, streamlined interface** with essential controls prominently displayed
- **Dedicated settings modal** for advanced configuration without cluttering main UI
- **Beautiful gradient design** with professional color palette
- **Responsive layout** that works on all screen sizes
- **Smooth animations** and hover effects throughout
- **Dark mode support** (automatically detects system preference)
- **Accessibility-first** design with ARIA labels and keyboard navigation

### 🛠️ **Powerful Text Processing**
- **Dual AI providers**: Local (Ollama) and Cloud (OpenRouter) support
- **Dynamic model discovery**: Automatically fetches available models from APIs
- **100+ AI models**: Access to Gemma, Llama, Claude, GPT-4, Mistral, and many more
- **Smart model caching**: Reduces API calls with intelligent caching system
- **Multiple languages** support (English, French, Spanish, German)
- **Tone adjustment** (Professional, Casual, Confident, Friendly)
- **Various actions** (Improve Style, Fix Grammar, Shorten, Expand)
- **Real-time character counting** with visual warnings
- **Text length validation** (up to 10,000 characters)
- **Secure API key management** with local storage

### ⚡ **Enhanced User Experience**
- **Clean main interface** with only essential controls visible
- **Settings modal** for advanced configuration (Alt+S to open)
- **Auto-save preferences** with persistent settings across sessions
- **Dynamic model loading** with refresh capability
- **One-click copy** with visual feedback
- **Clear button** for quick text reset
- **Loading states** with animated spinners
- **Status indicators** showing app state
- **Intelligent error handling** with actionable error messages
- **Model caching** for faster subsequent loads

### ⌨️ **Keyboard Shortcuts**
- `Ctrl+Enter` - Refine text
- `Ctrl+K` - Clear all text
- `Alt+S` - Open settings modal
- `Escape` - Close settings modal
- `Ctrl+C` - Copy output (when focused)

## 🚀 Getting Started

### Prerequisites

Smart Scribe supports two AI providers. Choose one or use both:

#### Option 1: Ollama (Local AI)
1. **Install Ollama**: Download from [ollama.ai](https://ollama.ai/)
2. **Download a model** (choose one):
   ```bash
   ollama pull gemma:2b-instruct    # Lightweight, fast
   ollama pull gemma:7b-instruct    # Better quality
   ollama pull llama3:8b-instruct   # Alternative option
   ```
3. **Start Ollama service**:
   ```bash
   ollama serve
   ```

#### Option 2: OpenRouter (Cloud AI)
1. **Get API Key**: Sign up at [openrouter.ai](https://openrouter.ai/) and get your API key from [here](https://openrouter.ai/keys)
2. **No local installation required** - just enter your API key in the app

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/abdelazizelm/smart-scribe.git
   cd smart-scribe
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run the application**:
   ```bash
   npm start
   ```

## 🏗️ Building

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Package for Distribution
```bash
npm run package
```

### Create Windows Executable
```bash
npm run make
```

This will create:
- `out/Smart Scribe-win32-x64/smart-scribe.exe` - Portable executable
- `out/make/squirrel.windows/x64/Smart-Scribe-Setup.exe` - Windows installer (~113 MB)

## 🎨 Screenshots

### Main Interface
The clean, modern interface with gradient backgrounds and intuitive controls.

### Text Processing
Real-time text refinement with loading states and visual feedback.

### Responsive Design
Seamlessly adapts to different screen sizes and orientations.

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Electron
- **AI Model**: Gemma 2B Instruct via Ollama
- **Styling**: Modern CSS with custom properties and animations
- **Typography**: Inter font family
- **Icons**: Unicode emojis for universal compatibility

## 📁 Project Structure

```
smart-scribe/
├── src/
│   ├── index.html          # Main application UI
│   ├── index.css           # Modern styling and animations
│   ├── renderer.js         # Frontend logic and API calls
│   ├── index.js            # Electron main process
│   └── preload.js          # Electron preload script
├── package.json            # Dependencies and scripts
├── forge.config.js         # Electron Forge configuration
└── README.md              # This file
```

## 🎯 Usage

1. **Launch the application**
2. **Configure settings** (first time only):
   - Click the settings button (⚙️) or press `Alt+S`
   - Choose your AI provider (Ollama or OpenRouter)
   - Select your preferred AI model
   - Enter OpenRouter API key if using cloud models
   - Adjust application preferences
3. **Use the main interface**:
   - Type or paste your text in the left panel
   - Choose action (Improve Style, Fix Grammar, Shorten, Expand)
   - Select tone (Professional, Casual, Confident, Friendly)
   - Pick target language
4. **Click "Refine Text"** or press `Ctrl+Enter`
5. **Copy the result** using the copy button or `Ctrl+C`

## 🔧 Configuration

### AI Provider Setup

#### Ollama (Local)
- Models are automatically detected from your local Ollama installation
- To add more models: `ollama pull model-name`
- Popular models: `gemma:7b-instruct`, `llama3:8b-instruct`, `mistral:7b-instruct`

#### OpenRouter (Cloud)
- Get your API key from [OpenRouter](https://openrouter.ai/keys)
- API key is securely stored locally in your browser
- Access to premium models like GPT-4, Claude 3, and more

### Available Models

**Dynamic Model Discovery:**
Smart Scribe automatically discovers and lists all available models from your chosen provider:

**Ollama Models:**
- Automatically detects all locally installed models
- Popular options: Gemma, Llama 3.2, Mistral, Code Llama, and more
- Use `ollama pull <model-name>` to install new models

**OpenRouter Models:**
- Access to 100+ models from leading AI providers
- Includes: GPT-4o, Claude 3.5, Gemma 2, Llama 3.1, Mistral, and many more
- Models are automatically filtered for text generation tasks
- Real-time pricing and context length information

### Customizing the UI

The application uses CSS custom properties for easy theming. Modify the `:root` variables in `src/index.css` to customize colors, spacing, and other design elements.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Gemma AI** by Google for the powerful language model
- **Ollama** for making AI models accessible locally
- **Electron** for enabling cross-platform desktop development
- **Inter Font** by Rasmus Andersson for beautiful typography

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/abdelazizelm/smart-scribe/issues) page
2. Create a new issue if your problem isn't already reported
3. Provide detailed information about your setup and the issue

---

<div align="center">

**Made with ❤️ by [Abdelaziz ELMEHAMMEDY](https://github.com/abdelazizelm)**

⭐ Star this repository if you find it helpful!

</div>
