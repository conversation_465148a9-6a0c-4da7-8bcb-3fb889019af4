/* Content Script Styles for Smart Scribe Extension */

/* Spin animation for loading indicators */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Base widget styles */
.smart-scribe-widget {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.smart-scribe-widget * {
  box-sizing: border-box;
}

/* Quick action button for selected text */
#smart-scribe-quick-action {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Text field widget */
#smart-scribe-text-widget {
  animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Action menu */
#smart-scribe-action-menu {
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Processing indicator */
#smart-scribe-processing {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover effects for interactive elements */
.smart-scribe-widget button {
  transition: all 0.2s ease;
}

.smart-scribe-widget button:active {
  transform: scale(0.95);
}

/* Focus styles for accessibility */
.smart-scribe-widget button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .smart-scribe-widget {
    font-size: 13px;
  }
  
  #smart-scribe-text-widget .smart-scribe-text-controls .action-btn,
  #smart-scribe-text-widget .smart-scribe-text-controls .open-popup-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  #smart-scribe-action-menu {
    min-width: 180px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .smart-scribe-widget {
    border: 2px solid currentColor;
  }
  
  #smart-scribe-quick-action {
    border: 2px solid white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .smart-scribe-widget,
  .smart-scribe-widget * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  #smart-scribe-text-widget,
  #smart-scribe-action-menu {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  #smart-scribe-text-widget .smart-scribe-text-controls .action-btn {
    background: #374151;
    color: #d1d5db;
  }
  
  #smart-scribe-text-widget .smart-scribe-text-controls .action-btn:hover {
    background: #2563eb;
    color: white;
  }
  
  .smart-scribe-menu .menu-action {
    color: #f9fafb;
  }
  
  .smart-scribe-menu .menu-action:hover {
    background: #374151;
    color: #60a5fa;
  }
  
  .smart-scribe-menu .menu-header {
    color: #9ca3af;
  }
  
  .smart-scribe-menu .menu-divider {
    background: #374151;
  }
}

/* Print styles - hide all widgets when printing */
@media print {
  .smart-scribe-widget {
    display: none !important;
  }
}
