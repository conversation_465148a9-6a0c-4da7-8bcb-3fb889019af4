const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');

module.exports = {
  packagerConfig: {
    asar: true,
    name: 'Smart Scribe',
    executableName: 'smart-scribe',
    appBundleId: 'com.abdelazizelm.smart-scribe',
    appCopyright: 'Copyright © 2024 Abdelaziz ELMEHAMMEDY',
    appVersion: '1.0.0',
    buildVersion: '1.0.0',
    // icon: './assets/icon', // Will look for icon.ico on Windows
    win32metadata: {
      CompanyName: 'Abdela<PERSON>z ELMEHAMMEDY',
      FileDescription: 'Smart Scribe - AI Writing Assistant',
      ProductName: 'Smart Scribe',
      InternalName: 'smart-scribe'
    }
  },
  rebuildConfig: {},
  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'smart-scribe',
        authors: '<PERSON><PERSON><PERSON><PERSON> ELMEHAMMEDY',
        description: 'AI-powered writing assistant with support for multiple AI providers',
        // setupIcon: './assets/icon.ico',
        // iconUrl: 'https://raw.githubusercontent.com/abdelazizelm/smart-scribe/master/assets/icon.ico',
        // loadingGif: './assets/loading.gif',
        noMsi: true,
        setupExe: 'Smart-Scribe-Setup.exe',
        setupMsi: 'Smart-Scribe-Setup.msi'
      },
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['darwin'],
    },
    {
      name: '@electron-forge/maker-deb',
      config: {},
    },
    {
      name: '@electron-forge/maker-rpm',
      config: {},
    },
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {},
    },
    // Fuses are used to enable/disable various Electron functionality
    // at package time, before code signing the application
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: true,
    }),
  ],
};
